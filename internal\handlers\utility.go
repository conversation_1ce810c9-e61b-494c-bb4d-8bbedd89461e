package handlers

import (
	"crypto/md5"
	"fmt"
	"log"
	"net/http"
	"time"

	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// GetServerTime BMS_SERVER_TIME - 获取服务器时间
func GetServerTime(c *gin.Context) {
	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: map[string]interface{}{
			"time": time.Now().Unix(),
		},
	})
}

// CheckIP BMS_IP_IS_ENABLE - IP检查
func CheckIP(c *gin.Context) {
	clientIP := c.ClientIP()

	// 模拟IP检查逻辑，实际应用中可以检查IP白名单/黑名单
	isEnabled := true
	if clientIP == "127.0.0.1" || clientIP == "::1" {
		isEnabled = true
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: map[string]interface{}{
			"ip":        clientIP,
			"is_enable": isEnabled,
		},
	})
}

// CheckLoginCode LOGINCODE - 登录码验证
func CheckLoginCode(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.LoginCodeRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.Code == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 场景1: 客户端请求时，ca_code 不存在，验证 code 是否有效
		if req.CACode == "" {
			log.Printf("验证码查询: app_name=%s, code=%s", req.AppName, req.Code)
			// 查询数据库中是否存在该验证码
			record, err := db.CheckLoginCode(req.AppName, req.Code, "")
			if err != nil {
				log.Printf("数据库查询失败: %v", err)
				c.JSON(http.StatusInternalServerError, models.StandardResponse{
					Code: -1,
					Msg:  "数据库查询失败",
				})
				return
			}

			// 如果验证码不存在，表示无效
			if record == nil {
				c.JSON(http.StatusOK, models.StandardResponse{
					Code: -1,
					Msg:  "验证码无效",
				})
				return
			}

			// 如果验证码存在且已经有 ca_code，表示已经使用过
			if record.CACode != "" {
				c.JSON(http.StatusOK, models.StandardResponse{
					Code: -1,
					Msg:  "验证码已使用",
				})
				return
			}

			// 验证码有效且未使用，生成 ca_code 并更新数据库
			hash := md5.Sum([]byte(req.Code))
			caCode := fmt.Sprintf("%x", hash)

			err = db.UpdateLoginCodeCACode(req.AppName, req.Code, caCode)
			if err != nil {
				c.JSON(http.StatusInternalServerError, models.StandardResponse{
					Code: -1,
					Msg:  "更新验证码失败",
				})
				return
			}

			// 返回成功响应
			responseData := map[string]interface{}{
				"app_name": req.AppName,
				"version":  req.Version,
				"code":     req.Code,
				"ca_code":  caCode,
			}

			c.JSON(http.StatusOK, models.StandardResponse{
				Code: 0,
				Msg:  "success",
				Data: responseData,
			})
			return
		}

		// 场景2: 客户端请求时，ca_code 存在，验证 code 和 ca_code 是否匹配
		record, err := db.CheckLoginCode(req.AppName, req.Code, req.CACode)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "数据库查询失败",
			})
			return
		}

		// 如果找不到匹配的记录，表示验证码过期或无效
		if record == nil {
			c.JSON(http.StatusOK, models.StandardResponse{
				Code: -1,
				Msg:  "验证码过期",
			})
			return
		}

		// 验证成功
		responseData := map[string]interface{}{
			"app_name": req.AppName,
			"version":  req.Version,
			"code":     req.Code,
			"ca_code":  req.CACode,
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: responseData,
		})
	}
}
