# 日志配置说明

## 概述

IAA GameLog 支持灵活的日志配置，包括日志级别、输出位置、格式以及 Gin HTTP 访问日志的自定义格式。

## 配置文件结构

```yaml
logging:
  level: info                    # 日志级别
  format: text                   # 日志格式
  output: stdout                 # 输出位置
  
  gin_format:                    # Gin HTTP访问日志格式
    template: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
    time_format: "2006-01-02 15:04:05"
    enable_color: false
```

## 日志级别 (level)

| 级别 | 说明 | Gin模式 | HTTP访问日志 |
|------|------|---------|-------------|
| `debug` | 显示所有调试信息 | Debug模式 | 记录所有请求 |
| `info` | 显示一般信息 | Release模式 | 记录所有请求 |
| `warn` | 只显示警告和错误 | Release模式 | 只记录4xx/5xx状态码 |
| `error` | 只显示错误 | Release模式 | 禁用HTTP访问日志 |

## 输出位置 (output)

- `stdout` - 标准输出（控制台）
- `stderr` - 错误输出
- `文件路径` - 输出到指定文件，如 `logs/app.log`

## Gin HTTP访问日志格式

### 支持的变量

| 变量 | 说明 | 示例 |
|------|------|------|
| `{time}` | 请求时间 | `2025-08-04 15:30:45` |
| `{method}` | HTTP方法 | `GET`, `POST` |
| `{path}` | 请求路径（含查询参数） | `/health?check=true` |
| `{status}` | HTTP状态码 | `200`, `404` |
| `{latency}` | 响应时间 | `1.2ms`, `500µs` |
| `{ip}` | 客户端IP | `127.0.0.1` |
| `{user_agent}` | 用户代理 | `Mozilla/5.0...` |
| `{error}` | 错误信息 | 错误详情 |

### 预设模板

#### 1. 详细格式（默认）
```yaml
template: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
```
输出示例：
```
[INFO] [2025-08-04 15:30:45] GET /health - 200 (1.2ms) from 127.0.0.1
```

#### 2. 简洁格式
```yaml
template: "{method} {path} - {status} ({latency})"
```
输出示例：
```
[INFO] GET /health - 200 (1.2ms)
```

#### 3. 开发格式
```yaml
template: "{method} {path} -> {status} in {latency}"
```
输出示例：
```
[INFO] GET /health -> 200 in 1.2ms
```

#### 4. 完整格式
```yaml
template: "[{time}] {ip} \"{method} {path}\" {status} {latency} \"{user_agent}\""
```
输出示例：
```
[INFO] [2025-08-04 15:30:45] 127.0.0.1 "GET /health" 200 1.2ms "Mozilla/5.0..."
```

### 时间格式 (time_format)

使用 Go 的时间格式规范：
- `2006-01-02 15:04:05` - 标准格式
- `2006/01/02 - 15:04:05` - 斜杠分隔
- `15:04:05` - 只显示时间
- `2006-01-02T15:04:05Z07:00` - RFC3339格式

### 颜色支持 (enable_color)

- `true` - 启用ANSI颜色代码（适合控制台输出）
- `false` - 禁用颜色（适合文件输出）

颜色方案：
- **状态码**：200-299绿色，300-399白色，400-499黄色，500+红色
- **HTTP方法**：GET蓝色，POST青色，PUT黄色，DELETE红色等

## 配置示例

### 开发环境配置
```yaml
logging:
  level: debug
  format: text
  output: stdout
  gin_format:
    template: "{method} {path} -> {status} in {latency}"
    time_format: "15:04:05"
    enable_color: true
```

### 生产环境配置
```yaml
logging:
  level: warn
  format: text
  output: logs/app.log
  gin_format:
    template: "[{time}] {ip} \"{method} {path}\" {status} {latency}"
    time_format: "2006-01-02T15:04:05Z07:00"
    enable_color: false
```

### 调试环境配置
```yaml
logging:
  level: debug
  format: text
  output: stdout
  gin_format:
    template: "[{time}] {method} {path} - {status} ({latency}) from {ip} - {user_agent}"
    time_format: "2006-01-02 15:04:05"
    enable_color: true
```

## 注意事项

1. **文件输出**：确保日志目录存在，程序会自动创建日志文件
2. **颜色输出**：文件输出时建议禁用颜色，避免ANSI代码污染日志文件
3. **性能考虑**：debug级别会产生大量日志，生产环境建议使用warn或error级别
4. **日志轮转**：程序不自动轮转日志文件，建议使用外部工具如logrotate
5. **实时生效**：修改配置后需要重启服务才能生效

## 故障排除

### 问题：日志文件无法创建
**解决**：检查目录权限，确保程序有写入权限

### 问题：颜色代码显示异常
**解决**：在不支持ANSI的终端中设置 `enable_color: false`

### 问题：日志格式不生效
**解决**：检查YAML语法，确保缩进正确，重启服务
