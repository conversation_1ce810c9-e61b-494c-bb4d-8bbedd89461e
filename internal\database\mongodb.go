package database

import (
	"context"
	"log"
	"time"

	"iaa-gamelog/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoGameRecord MongoDB游戏记录结构体
type MongoGameRecord struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID    string             `json:"user_id" bson:"user_id"`
	GameName  string             `json:"game_name" bson:"game_name"`
	GameData  interface{}        `json:"game_data" bson:"game_data"` // 直接存储JSON对象
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

// MongoWhitelist MongoDB白名单记录结构体
type MongoWhitelist struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	AppName   string             `json:"app_name" bson:"app_name"`
	UUID      string             `json:"uuid" bson:"uuid"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

// MongoCACode MongoDB验证码记录结构体
type MongoCACode struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	AppName   string             `json:"app_name" bson:"app_name"`
	Code      string             `json:"code" bson:"code"`
	CACode    string             `json:"ca_code" bson:"ca_code"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

// MongoDatabase MongoDB数据库操作结构体
type MongoDatabase struct {
	client              *mongo.Client
	database            *mongo.Database
	collection          *mongo.Collection
	whitelistCollection *mongo.Collection
	caCodeCollection    *mongo.Collection
}

// NewMongoDatabase 创建新的MongoDB数据库连接
func NewMongoDatabase(uri, dbName, collectionName string) (*MongoDatabase, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 连接MongoDB
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}

	// 测试连接
	err = client.Ping(ctx, nil)
	if err != nil {
		return nil, err
	}

	database := client.Database(dbName)
	collection := database.Collection(collectionName)

	// 创建复合索引
	indexModel := mongo.IndexModel{
		Keys:    bson.D{{"user_id", 1}, {"game_name", 1}},
		Options: options.Index().SetUnique(true),
	}

	_, err = collection.Indexes().CreateOne(ctx, indexModel)
	if err != nil {
		log.Printf("创建索引失败: %v", err)
	}

	// 创建其他索引
	collection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"user_id", 1}},
	})
	collection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"game_name", 1}},
	})
	collection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"created_at", -1}},
	})
	collection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"updated_at", -1}},
	})

	// 初始化白名单集合
	whitelistCollection := database.Collection("whitelist")

	// 创建白名单复合索引
	whitelistIndexModel := mongo.IndexModel{
		Keys:    bson.D{{"app_name", 1}, {"uuid", 1}},
		Options: options.Index().SetUnique(true),
	}

	_, err = whitelistCollection.Indexes().CreateOne(ctx, whitelistIndexModel)
	if err != nil {
		log.Printf("创建白名单索引失败: %v", err)
	}

	// 创建白名单其他索引
	whitelistCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"app_name", 1}},
	})
	whitelistCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"uuid", 1}},
	})
	whitelistCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"created_at", -1}},
	})

	// 初始化验证码集合
	caCodeCollection := database.Collection("ca_code_list")

	// 创建验证码复合索引
	caCodeIndexModel := mongo.IndexModel{
		Keys:    bson.D{{"app_name", 1}, {"code", 1}},
		Options: options.Index().SetUnique(true),
	}

	_, err = caCodeCollection.Indexes().CreateOne(ctx, caCodeIndexModel)
	if err != nil {
		log.Printf("创建验证码索引失败: %v", err)
	}

	// 创建验证码其他索引
	caCodeCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"app_name", 1}},
	})
	caCodeCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"code", 1}},
	})
	caCodeCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"ca_code", 1}},
	})
	caCodeCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{"created_at", -1}},
	})

	return &MongoDatabase{
		client:              client,
		database:            database,
		collection:          collection,
		whitelistCollection: whitelistCollection,
		caCodeCollection:    caCodeCollection,
	}, nil
}

// Close 关闭MongoDB连接
func (m *MongoDatabase) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return m.client.Disconnect(ctx)
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (m *MongoDatabase) SaveRecord(userID, gameName string, gameData string) (*MongoGameRecord, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	now := time.Now()
	filter := bson.M{"user_id": userID, "game_name": gameName}

	// 使用upsert操作
	update := bson.M{
		"$set": bson.M{
			"user_id":    userID,
			"game_name":  gameName,
			"game_data":  gameData,
			"updated_at": now,
		},
		"$setOnInsert": bson.M{
			"created_at": now,
		},
	}

	opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)

	var result MongoGameRecord
	err := m.collection.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
	if err != nil {
		return nil, "", err
	}

	// 判断是创建还是更新
	action := "updated"
	if result.CreatedAt.Equal(result.UpdatedAt) {
		action = "created"
	}

	return &result, action, nil
}

// GetRecords 获取游戏记录
func (m *MongoDatabase) GetRecords(userID, gameName string, limit, offset int) ([]MongoGameRecord, int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if userID != "" {
		filter["user_id"] = userID
	}
	if gameName != "" {
		filter["game_name"] = gameName
	}

	// 获取总数
	total, err := m.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 查询选项
	findOptions := options.Find()
	findOptions.SetSort(bson.D{{"updated_at", -1}})
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(offset))

	// 执行查询
	cursor, err := m.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var records []MongoGameRecord
	for cursor.Next(ctx) {
		var record MongoGameRecord
		if err := cursor.Decode(&record); err != nil {
			return nil, 0, err
		}
		records = append(records, record)
	}

	if err := cursor.Err(); err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetRecordByID 根据ID获取记录
func (m *MongoDatabase) GetRecordByID(id string) (*MongoGameRecord, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var record MongoGameRecord
	err = m.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&record)
	if err != nil {
		return nil, err
	}

	return &record, nil
}

// DeleteRecord 删除记录
func (m *MongoDatabase) DeleteRecord(userID, gameName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{"user_id": userID, "game_name": gameName}
	_, err := m.collection.DeleteOne(ctx, filter)
	return err
}

// ==================== 白名单相关方法 ====================

// CheckWhitelist 检查UUID是否在白名单中
func (m *MongoDatabase) CheckWhitelist(appName, uuid string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{"app_name": appName, "uuid": uuid}
	count, err := m.whitelistCollection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// AddToWhitelist 添加UUID到白名单
func (m *MongoDatabase) AddToWhitelist(appName, uuid string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	now := time.Now()
	filter := bson.M{"app_name": appName, "uuid": uuid}

	update := bson.M{
		"$set": bson.M{
			"app_name":   appName,
			"uuid":       uuid,
			"updated_at": now,
		},
		"$setOnInsert": bson.M{
			"created_at": now,
		},
	}

	opts := options.FindOneAndUpdate().SetUpsert(true)
	err := m.whitelistCollection.FindOneAndUpdate(ctx, filter, update, opts).Err()
	return err
}

// RemoveFromWhitelist 从白名单中移除UUID
func (m *MongoDatabase) RemoveFromWhitelist(appName, uuid string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{"app_name": appName, "uuid": uuid}
	result, err := m.whitelistCollection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// GetWhitelistRecords 获取白名单记录
func (m *MongoDatabase) GetWhitelistRecords(appName string, limit, offset int) (interface{}, int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if appName != "" {
		filter["app_name"] = appName
	}

	// 获取总数
	total, err := m.whitelistCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 查询选项
	findOptions := options.Find()
	findOptions.SetSort(bson.D{{"created_at", -1}})
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(offset))

	// 执行查询
	cursor, err := m.whitelistCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var records []MongoWhitelist
	for cursor.Next(ctx) {
		var record MongoWhitelist
		if err := cursor.Decode(&record); err != nil {
			return nil, 0, err
		}
		records = append(records, record)
	}

	if err := cursor.Err(); err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// ==================== 验证码相关方法 ====================

// CheckLoginCode 检查验证码
func (m *MongoDatabase) CheckLoginCode(appName, code, caCode string) (*models.LoginCodeRecord, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var filter bson.M
	if caCode == "" {
		// 查询 code 是否存在
		filter = bson.M{"app_name": appName, "code": code}
	} else {
		// 验证 code 和 ca_code 是否匹配
		filter = bson.M{"app_name": appName, "code": code, "ca_code": caCode}
	}

	var mongoRecord MongoCACode
	err := m.caCodeCollection.FindOne(ctx, filter).Decode(&mongoRecord)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // 记录不存在
		}
		return nil, err
	}

	// 转换为统一格式
	record := &models.LoginCodeRecord{
		ID:        mongoRecord.ID.Hex(),
		AppName:   mongoRecord.AppName,
		Code:      mongoRecord.Code,
		CACode:    mongoRecord.CACode,
		CreatedAt: mongoRecord.CreatedAt,
		UpdatedAt: mongoRecord.UpdatedAt,
	}

	return record, nil
}

// UpdateLoginCodeCACode 更新验证码的 ca_code
func (m *MongoDatabase) UpdateLoginCodeCACode(appName, code, caCode string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	now := time.Now()
	filter := bson.M{"app_name": appName, "code": code}

	update := bson.M{
		"$set": bson.M{
			"app_name":   appName,
			"code":       code,
			"ca_code":    caCode,
			"updated_at": now,
		},
		"$setOnInsert": bson.M{
			"created_at": now,
		},
	}

	opts := options.FindOneAndUpdate().SetUpsert(true)
	err := m.caCodeCollection.FindOneAndUpdate(ctx, filter, update, opts).Err()
	return err
}
