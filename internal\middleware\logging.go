package middleware

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"

	"iaa-gamelog/internal/config"

	"github.com/gin-gonic/gin"
)

// SetupGinLogging 根据配置设置 Gin 的日志中间件
func SetupGinLogging(loggingConfig *config.LoggingConfig) gin.HandlerFunc {
	// 根据日志级别决定是否启用 Gin 日志
	level := strings.ToLower(loggingConfig.Level)

	// 如果是 error 级别，禁用 Gin 的访问日志
	if level == "error" {
		gin.DisableConsoleColor()
		gin.DefaultWriter = io.Discard
		return gin.LoggerWithWriter(io.Discard)
	}

	// 设置 Gin 日志输出
	var output io.Writer
	switch strings.ToLower(loggingConfig.Output) {
	case "stdout", "":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 如果是文件路径，使用文件输出
		if file, err := os.OpenFile(loggingConfig.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
			output = file
		} else {
			output = os.Stdout
			log.Printf("无法打开日志文件 %s，使用标准输出: %v", loggingConfig.Output, err)
		}
	}

	// 根据格式设置颜色
	if strings.ToLower(loggingConfig.Format) == "json" {
		gin.DisableConsoleColor()
	}

	// 设置 Gin 的默认输出
	gin.DefaultWriter = output

	// 根据日志级别返回不同的中间件
	switch level {
	case "debug":
		// Debug 级别：显示详细信息
		return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
			return formatGinLog(param, "DEBUG", &loggingConfig.GinFormat)
		})
	case "info", "":
		// Info 级别：标准访问日志
		return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
			return formatGinLog(param, "INFO", &loggingConfig.GinFormat)
		})
	case "warn", "warning":
		// Warn 级别：只记录错误和警告
		return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
			if param.StatusCode >= 400 {
				return formatGinLog(param, "WARN", &loggingConfig.GinFormat)
			}
			return ""
		})
	default:
		return gin.Logger()
	}
}

// formatGinLog 格式化 Gin 日志输出
func formatGinLog(param gin.LogFormatterParams, level string, ginConfig *config.GinFormatConfig) string {
	// 如果没有配置，使用默认格式
	if ginConfig.Template == "" {
		ginConfig.Template = "[{time}] {method} {path} - {status} ({latency}) from {ip}"
	}
	if ginConfig.TimeFormat == "" {
		ginConfig.TimeFormat = "2006-01-02 15:04:05"
	}

	// 准备替换变量
	template := ginConfig.Template

	// 格式化时间
	timeStr := param.TimeStamp.Format(ginConfig.TimeFormat)

	// 格式化状态码（带颜色或不带颜色）
	var statusStr string
	if ginConfig.EnableColor {
		statusColor := getStatusColor(param.StatusCode)
		resetColor := "\033[0m"
		statusStr = fmt.Sprintf("%s%d%s", statusColor, param.StatusCode, resetColor)
	} else {
		statusStr = fmt.Sprintf("%d", param.StatusCode)
	}

	// 格式化HTTP方法（带颜色或不带颜色）
	var methodStr string
	if ginConfig.EnableColor {
		methodColor := getMethodColor(param.Method)
		resetColor := "\033[0m"
		methodStr = fmt.Sprintf("%s%s%s", methodColor, param.Method, resetColor)
	} else {
		methodStr = param.Method
	}

	// 格式化延迟时间
	latencyStr := param.Latency.String()

	// 构建完整路径（包含查询参数）
	fullPath := param.Path
	if param.Request.URL.RawQuery != "" {
		fullPath = param.Path + "?" + param.Request.URL.RawQuery
	}

	// 替换模板变量
	template = strings.ReplaceAll(template, "{time}", timeStr)
	template = strings.ReplaceAll(template, "{method}", methodStr)
	template = strings.ReplaceAll(template, "{path}", fullPath)
	template = strings.ReplaceAll(template, "{status}", statusStr)
	template = strings.ReplaceAll(template, "{latency}", latencyStr)
	template = strings.ReplaceAll(template, "{ip}", param.ClientIP)
	template = strings.ReplaceAll(template, "{user_agent}", param.Request.UserAgent())
	template = strings.ReplaceAll(template, "{error}", param.ErrorMessage)

	// 添加日志级别前缀
	return fmt.Sprintf("[%s] %s\n", level, template)
}

// getStatusColor 根据状态码获取颜色
func getStatusColor(code int) string {
	switch {
	case code >= 200 && code < 300:
		return "\033[97;42m" // 绿色背景
	case code >= 300 && code < 400:
		return "\033[90;47m" // 白色背景
	case code >= 400 && code < 500:
		return "\033[90;43m" // 黄色背景
	default:
		return "\033[97;41m" // 红色背景
	}
}

// getMethodColor 根据请求方法获取颜色
func getMethodColor(method string) string {
	switch method {
	case "GET":
		return "\033[97;44m" // 蓝色背景
	case "POST":
		return "\033[97;46m" // 青色背景
	case "PUT":
		return "\033[97;43m" // 黄色背景
	case "DELETE":
		return "\033[97;41m" // 红色背景
	case "PATCH":
		return "\033[97;42m" // 绿色背景
	case "HEAD":
		return "\033[97;45m" // 紫色背景
	case "OPTIONS":
		return "\033[90;47m" // 白色背景
	default:
		return "\033[0m" // 默认颜色
	}
}
