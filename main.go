package main

import (
	"fmt"
	"log"

	"iaa-gamelog/internal/config"
	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/router"
)

var (
	db        database.DatabaseInterface
	appConfig *config.Config
)

// initDB 初始化数据库
func initDB(config *config.Config) {
	var err error
	switch config.Database.Type {
	case "mongodb":
		db, err = database.NewMongoAdapter(
			config.Database.MongoDB.URI,
			config.Database.MongoDB.Database,
			config.Database.MongoDB.Collection,
		)
		if err != nil {
			log.Fatal("MongoDB连接失败:", err)
		}
		log.Println("MongoDB数据库初始化成功")
	case "sqlserver":
		db, err = database.NewSQLServerAdapter(
			config.Database.SQLServer.Server,
			config.Database.SQLServer.Port,
			config.Database.SQLServer.Database,
			config.Database.SQLServer.Username,
			config.Database.SQLServer.Password,
			config.Database.SQLServer.Instance,
			config.Database.SQLServer.Encrypt,
		)
		if err != nil {
			log.Fatal("SQL Server连接失败:", err)
		}
		log.Println("SQL Server数据库初始化成功")
	default:
		log.Fatal("不支持的数据库类型:", config.Database.Type)
	}
}

func main() {
	// 加载配置文件
	var err error
	appConfig, err = config.LoadConfig("")
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 初始化数据库
	initDB(appConfig)
	defer db.Close()

	// 设置路由
	r := router.SetupRouter(appConfig, db)

	// 服务器地址
	serverAddr := fmt.Sprintf("%s:%d", appConfig.Server.Host, appConfig.Server.Port)
	log.Printf("服务器启动在 %s", serverAddr)

	// 打印路由信息
	router.LogRoutes()

	// 启动服务器
	if err := r.Run(serverAddr); err != nil {
		log.Fatal("服务器启动失败:", err)
	}
}
