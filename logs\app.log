[INFO] 2025/08/04 15:23:59 config.go:272: 日志系统初始化完成 - Level: info, Format: text, Output: logs/app.log
[INFO] 2025/08/04 15:23:59 sqlserver.go:150: SQL Server表和索引创建成功
[INFO] 2025/08/04 15:23:59 sqlserver.go:54: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:23:59 main.go:44: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:23:59 router.go:45: CORS中间件已启用
[INFO] 2025/08/04 15:23:59 main.go:72: 服务器启动在 0.0.0.0:3001
[INFO] 2025/08/04 15:23:59 router.go:100: API路由信息:
[INFO] 2025/08/04 15:23:59 router.go:101: 
[INFO] 2025/08/04 15:23:59 router.go:102: 通用接口 (/common):
[INFO] 2025/08/04 15:23:59 router.go:103:   游戏配置:
[INFO] 2025/08/04 15:23:59 router.go:104:     GET  /common/config/info - 获取游戏启动配置 (BMS_LAUNCH_CONFIG)
[INFO] 2025/08/04 15:23:59 router.go:105:     GET  /common/config/share_list - 获取分享配置 (BMS_SHARE_CONFIG)
[INFO] 2025/08/04 15:23:59 router.go:106:   数据存储:
[INFO] 2025/08/04 15:23:59 router.go:107:     POST /common/game-data/s-save - 保存游戏数据 (DATA_SAVE, 需要签名验证)
[INFO] 2025/08/04 15:23:59 router.go:108:     GET  /common/game-data/get - 获取游戏数据 (DATA_GET)
[INFO] 2025/08/04 15:23:59 router.go:109:     GET  /common/game-data/multi-get - 批量获取游戏数据 (DATA_MULTIGET)
[INFO] 2025/08/04 15:23:59 router.go:110: 
[INFO] 2025/08/04 15:23:59 router.go:111: 检查工具 (/check):
[INFO] 2025/08/04 15:23:59 router.go:112:     GET  /check/time - 获取服务器时间 (BMS_SERVER_TIME)
[INFO] 2025/08/04 15:23:59 router.go:113:     GET  /check/ip/is - IP检查 (BMS_IP_IS_ENABLE)
[INFO] 2025/08/04 15:23:59 router.go:114:     GET  /check/login-code - 登录码验证 (LOGINCODE)
[INFO] 2025/08/04 15:23:59 router.go:115:     GET  /check/uuidInWhitelist - UUID白名单检查 (UUID_IN_WHITE_LIST, 需要签名验证)
[INFO] 2025/08/04 15:23:59 router.go:116: 
[INFO] 2025/08/04 15:23:59 router.go:117: 统计上报 (/statistics):
[INFO] 2025/08/04 15:23:59 router.go:118:     POST /statistics/login_log - 登录日志统计 (BMS_LOGIN_LOG)
[INFO] 2025/08/04 15:23:59 router.go:119:     POST /statistics/game - 游戏行为统计 (BMS_GAME)
[INFO] 2025/08/04 15:23:59 router.go:120:     POST /statistics/ad/show - 广告展示统计 (BMS_AD_SHOW)
[INFO] 2025/08/04 15:23:59 router.go:121:     POST /statistics/ad/hit - 广告点击统计 (BMS_AD_HIT)
[INFO] 2025/08/04 15:23:59 router.go:122:     POST /statistics/share/show - 分享展示统计 (BMS_SHARE_SHOW)
[INFO] 2025/08/04 15:23:59 router.go:123:     POST /statistics/hint - 提示统计 (BMS_HINT)
[INFO] 2025/08/04 15:23:59 router.go:124: 
[INFO] 2025/08/04 15:23:59 router.go:125: 白名单管理 (/whitelist, 需要签名验证):
[INFO] 2025/08/04 15:23:59 router.go:126:     POST   /whitelist/add - 添加到白名单
[INFO] 2025/08/04 15:23:59 router.go:127:     DELETE /whitelist/remove - 从白名单移除
[INFO] 2025/08/04 15:23:59 router.go:128:     GET    /whitelist/list - 获取白名单记录
[INFO] 2025/08/04 15:23:59 router.go:129:     GET    /whitelist/check - 检查白名单状态（详细版本）
[INFO] 2025/08/04 15:23:59 router.go:130: 
[INFO] 2025/08/04 15:23:59 router.go:131: 系统:
[INFO] 2025/08/04 15:23:59 router.go:132:     GET  /health - 健康检查
[INFO] 2025/08/04 - 15:24:15 |[97;42m 200 [0m|            0s |             ::1 |[97;44m GET     [0m /health
%!(EXTRA string=)[INFO] 2025/08/04 15:30:15 config.go:272: 日志系统初始化完成 - Level: info, Format: text, Output: logs/app.log
[INFO] 2025/08/04 15:30:15 sqlserver.go:150: SQL Server表和索引创建成功
[INFO] 2025/08/04 15:30:15 sqlserver.go:54: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:30:15 main.go:44: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:30:15 router.go:45: CORS中间件已启用
[INFO] 2025/08/04 15:30:15 main.go:72: 服务器启动在 0.0.0.0:3001
[INFO] 2025/08/04 15:30:15 router.go:100: API路由信息:
[INFO] 2025/08/04 15:30:15 router.go:101: 
[INFO] 2025/08/04 15:30:15 router.go:102: 通用接口 (/common):
[INFO] 2025/08/04 15:30:15 router.go:103:   游戏配置:
[INFO] 2025/08/04 15:30:15 router.go:104:     GET  /common/config/info - 获取游戏启动配置 (BMS_LAUNCH_CONFIG)
[INFO] 2025/08/04 15:30:15 router.go:105:     GET  /common/config/share_list - 获取分享配置 (BMS_SHARE_CONFIG)
[INFO] 2025/08/04 15:30:15 router.go:106:   数据存储:
[INFO] 2025/08/04 15:30:15 router.go:107:     POST /common/game-data/s-save - 保存游戏数据 (DATA_SAVE, 需要签名验证)
[INFO] 2025/08/04 15:30:15 router.go:108:     GET  /common/game-data/get - 获取游戏数据 (DATA_GET)
[INFO] 2025/08/04 15:30:15 router.go:109:     GET  /common/game-data/multi-get - 批量获取游戏数据 (DATA_MULTIGET)
[INFO] 2025/08/04 15:30:15 router.go:110: 
[INFO] 2025/08/04 15:30:15 router.go:111: 检查工具 (/check):
[INFO] 2025/08/04 15:30:15 router.go:112:     GET  /check/time - 获取服务器时间 (BMS_SERVER_TIME)
[INFO] 2025/08/04 15:30:15 router.go:113:     GET  /check/ip/is - IP检查 (BMS_IP_IS_ENABLE)
[INFO] 2025/08/04 15:30:15 router.go:114:     GET  /check/login-code - 登录码验证 (LOGINCODE)
[INFO] 2025/08/04 15:30:15 router.go:115:     GET  /check/uuidInWhitelist - UUID白名单检查 (UUID_IN_WHITE_LIST, 需要签名验证)
[INFO] 2025/08/04 15:30:15 router.go:116: 
[INFO] 2025/08/04 15:30:15 router.go:117: 统计上报 (/statistics):
[INFO] 2025/08/04 15:30:15 router.go:118:     POST /statistics/login_log - 登录日志统计 (BMS_LOGIN_LOG)
[INFO] 2025/08/04 15:30:15 router.go:119:     POST /statistics/game - 游戏行为统计 (BMS_GAME)
[INFO] 2025/08/04 15:30:15 router.go:120:     POST /statistics/ad/show - 广告展示统计 (BMS_AD_SHOW)
[INFO] 2025/08/04 15:30:15 router.go:121:     POST /statistics/ad/hit - 广告点击统计 (BMS_AD_HIT)
[INFO] 2025/08/04 15:30:15 router.go:122:     POST /statistics/share/show - 分享展示统计 (BMS_SHARE_SHOW)
[INFO] 2025/08/04 15:30:15 router.go:123:     POST /statistics/hint - 提示统计 (BMS_HINT)
[INFO] 2025/08/04 15:30:15 router.go:124: 
[INFO] 2025/08/04 15:30:15 router.go:125: 白名单管理 (/whitelist, 需要签名验证):
[INFO] 2025/08/04 15:30:15 router.go:126:     POST   /whitelist/add - 添加到白名单
[INFO] 2025/08/04 15:30:15 router.go:127:     DELETE /whitelist/remove - 从白名单移除
[INFO] 2025/08/04 15:30:15 router.go:128:     GET    /whitelist/list - 获取白名单记录
[INFO] 2025/08/04 15:30:15 router.go:129:     GET    /whitelist/check - 检查白名单状态（详细版本）
[INFO] 2025/08/04 15:30:15 router.go:130: 
[INFO] 2025/08/04 15:30:15 router.go:131: 系统:
[INFO] 2025/08/04 15:30:15 router.go:132:     GET  /health - 健康检查
[INFO] 2025/08/04 15:30:38 config.go:272: 日志系统初始化完成 - Level: info, Format: text, Output: logs/app.log
[INFO] 2025/08/04 15:30:38 sqlserver.go:150: SQL Server表和索引创建成功
[INFO] 2025/08/04 15:30:38 sqlserver.go:54: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:30:38 main.go:44: SQL Server数据库初始化成功
[INFO] 2025/08/04 15:30:38 router.go:45: CORS中间件已启用
[INFO] 2025/08/04 15:30:38 main.go:72: 服务器启动在 0.0.0.0:3001
[INFO] 2025/08/04 15:30:38 router.go:100: API路由信息:
[INFO] 2025/08/04 15:30:38 router.go:101: 
[INFO] 2025/08/04 15:30:38 router.go:102: 通用接口 (/common):
[INFO] 2025/08/04 15:30:38 router.go:103:   游戏配置:
[INFO] 2025/08/04 15:30:38 router.go:104:     GET  /common/config/info - 获取游戏启动配置 (BMS_LAUNCH_CONFIG)
[INFO] 2025/08/04 15:30:38 router.go:105:     GET  /common/config/share_list - 获取分享配置 (BMS_SHARE_CONFIG)
[INFO] 2025/08/04 15:30:38 router.go:106:   数据存储:
[INFO] 2025/08/04 15:30:38 router.go:107:     POST /common/game-data/s-save - 保存游戏数据 (DATA_SAVE, 需要签名验证)
[INFO] 2025/08/04 15:30:38 router.go:108:     GET  /common/game-data/get - 获取游戏数据 (DATA_GET)
[INFO] 2025/08/04 15:30:38 router.go:109:     GET  /common/game-data/multi-get - 批量获取游戏数据 (DATA_MULTIGET)
[INFO] 2025/08/04 15:30:38 router.go:110: 
[INFO] 2025/08/04 15:30:38 router.go:111: 检查工具 (/check):
[INFO] 2025/08/04 15:30:38 router.go:112:     GET  /check/time - 获取服务器时间 (BMS_SERVER_TIME)
[INFO] 2025/08/04 15:30:38 router.go:113:     GET  /check/ip/is - IP检查 (BMS_IP_IS_ENABLE)
[INFO] 2025/08/04 15:30:38 router.go:114:     GET  /check/login-code - 登录码验证 (LOGINCODE)
[INFO] 2025/08/04 15:30:38 router.go:115:     GET  /check/uuidInWhitelist - UUID白名单检查 (UUID_IN_WHITE_LIST, 需要签名验证)
[INFO] 2025/08/04 15:30:38 router.go:116: 
[INFO] 2025/08/04 15:30:38 router.go:117: 统计上报 (/statistics):
[INFO] 2025/08/04 15:30:38 router.go:118:     POST /statistics/login_log - 登录日志统计 (BMS_LOGIN_LOG)
[INFO] 2025/08/04 15:30:38 router.go:119:     POST /statistics/game - 游戏行为统计 (BMS_GAME)
[INFO] 2025/08/04 15:30:38 router.go:120:     POST /statistics/ad/show - 广告展示统计 (BMS_AD_SHOW)
[INFO] 2025/08/04 15:30:38 router.go:121:     POST /statistics/ad/hit - 广告点击统计 (BMS_AD_HIT)
[INFO] 2025/08/04 15:30:38 router.go:122:     POST /statistics/share/show - 分享展示统计 (BMS_SHARE_SHOW)
[INFO] 2025/08/04 15:30:38 router.go:123:     POST /statistics/hint - 提示统计 (BMS_HINT)
[INFO] 2025/08/04 15:30:38 router.go:124: 
[INFO] 2025/08/04 15:30:38 router.go:125: 白名单管理 (/whitelist, 需要签名验证):
[INFO] 2025/08/04 15:30:38 router.go:126:     POST   /whitelist/add - 添加到白名单
[INFO] 2025/08/04 15:30:38 router.go:127:     DELETE /whitelist/remove - 从白名单移除
[INFO] 2025/08/04 15:30:38 router.go:128:     GET    /whitelist/list - 获取白名单记录
[INFO] 2025/08/04 15:30:38 router.go:129:     GET    /whitelist/check - 检查白名单状态（详细版本）
[INFO] 2025/08/04 15:30:38 router.go:130: 
[INFO] 2025/08/04 15:30:38 router.go:131: 系统:
[INFO] 2025/08/04 15:30:38 router.go:132:     GET  /health - 健康检查
[INFO] 2025/08/04 - 15:31:00 |[97;42m 200 [0m|            0s |             ::1 |[97;44m GET     [0m /health
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[97;42m 200 [0m|       512.2µs |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[97;42m 200 [0m|       524.8µs |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=2.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[97;42m 200 [0m|      1.7737ms |             ::1 |[97;44m GET     [0m /common/config/info?app_name=another_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 400 [0m|            0s |             ::1 |[97;44m GET     [0m /common/config/info?app_name=&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 400 [0m|            0s |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 404 [0m|            0s |             ::1 |[97;44m GET     [0m /common/game/share_list?app_name=test_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 404 [0m|            0s |             ::1 |[97;44m GET     [0m /common/game/share_list?app_name=social_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 404 [0m|            0s |             ::1 |[97;44m GET     [0m /common/game/ads?app_name=test_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:31:00 |[90;43m 404 [0m|            0s |             ::1 |[97;44m GET     [0m /common/game/ads?app_name=ad_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:54 |[97;42m 200 [0m|       639.5µs |             ::1 |[97;44m GET     [0m /health
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:54 |[97;42m 200 [0m|      1.4717ms |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:54 |[97;42m 200 [0m|       682.9µs |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=2.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:54 |[97;42m 200 [0m|       599.1µs |             ::1 |[97;44m GET     [0m /common/config/info?app_name=another_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:55 |[90;43m 400 [0m|            0s |             ::1 |[97;44m GET     [0m /common/config/info?app_name=&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:55 |[90;43m 400 [0m|            0s |             ::1 |[97;44m GET     [0m /common/config/info?app_name=test_game&version=
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:55 |[97;42m 200 [0m|       561.7µs |             ::1 |[97;44m GET     [0m /common/config/share_list?app_name=test_game&version=1.0.0
%!(EXTRA string=)[INFO] 2025/08/04 - 15:32:55 |[97;42m 200 [0m|            0s |             ::1 |[97;44m GET     [0m /common/config/share_list?app_name=social_game&version=1.0.0
%!(EXTRA string=)