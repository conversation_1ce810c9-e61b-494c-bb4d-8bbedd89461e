package handlers

import (
	"net/http"
	"strconv"

	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// AddToWhitelist 添加UUID到白名单
func AddToWhitelist(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.WhitelistRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "参数格式错误",
			})
			return
		}

		err := db.AddToWhitelist(req.AppName, req.UUID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "添加到白名单失败",
			})
			return
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "添加成功",
		})
	}
}

// RemoveFromWhitelist 从白名单中移除UUID
func RemoveFromWhitelist(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		appName := c.Query("app_name")
		uuid := c.Query("uuid")

		if appName == "" || uuid == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		err := db.RemoveFromWhitelist(appName, uuid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "从白名单移除失败",
			})
			return
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "移除成功",
		})
	}
}

// GetWhitelistRecords 获取白名单记录
func GetWhitelistRecords(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		appName := c.Query("app_name")
		limitStr := c.DefaultQuery("limit", "10")
		offsetStr := c.DefaultQuery("offset", "0")

		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit <= 0 {
			limit = 10
		}
		if limit > 100 {
			limit = 100 // 限制最大返回数量
		}

		offset, err := strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			offset = 0
		}

		records, total, err := db.GetWhitelistRecords(appName, limit, offset)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "获取白名单记录失败",
			})
			return
		}

		responseData := map[string]interface{}{
			"records": records,
			"total":   total,
			"limit":   limit,
			"offset":  offset,
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: responseData,
		})
	}
}

// CheckWhitelistStatus 检查白名单状态（详细版本）
func CheckWhitelistStatus(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		uuid := c.Query("uuid")
		appName := c.Query("app_name")

		if uuid == "" || appName == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		isInWhitelist, err := db.CheckWhitelist(appName, uuid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "数据库查询失败",
			})
			return
		}

		responseData := map[string]interface{}{
			"uuid":         uuid,
			"app_name":     appName,
			"in_whitelist": isInWhitelist,
			"status":       isInWhitelist,
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: responseData,
		})
	}
}
