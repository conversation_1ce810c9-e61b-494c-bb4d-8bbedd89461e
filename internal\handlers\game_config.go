package handlers

import (
	"encoding/json"
	"net/http"
	"os"

	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// readInfoJSON 读取根目录下的 info.json 文件
func readInfoJSON() (map[string]interface{}, error) {
	// 读取 info.json 文件
	data, err := os.ReadFile("info.json")
	if err != nil {
		return nil, err
	}

	// 解析 JSON
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		return nil, err
	}

	// 返回 data 字段的内容，如果存在的话
	if dataField, exists := result["data"]; exists {
		if dataMap, ok := dataField.(map[string]interface{}); ok {
			return dataMap, nil
		}
	}

	// 如果没有 data 字段，返回整个对象
	return result, nil
}

// GetLaunchConfig BMS_LAUNCH_CONFIG - 获取游戏启动配置
func GetLaunchConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 读取根目录下的 info.json 文件
	infoData, err := readInfoJSON()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.StandardResponse{
			Code: -1,
			Msg:  "读取配置文件失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: infoData,
	})
}

// GetShareConfig BMS_SHARE_CONFIG - 获取分享配置
func GetShareConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟分享配置数据
	shareData := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"list": []map[string]interface{}{
			{
				"id":          1,
				"title":       "分享游戏给好友",
				"description": "邀请好友一起玩游戏",
				"image_url":   "https://example.com/share1.jpg",
				"reward":      100,
			},
			{
				"id":          2,
				"title":       "分享到朋友圈",
				"description": "分享游戏到朋友圈",
				"image_url":   "https://example.com/share2.jpg",
				"reward":      200,
			},
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: shareData,
	})
}

// GetAdsConfig BMS_TOFU_CONFIG - 获取广告配置
func GetAdsConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟广告配置数据
	adsData := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"ads": map[string]interface{}{
			"banner": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "banner_ad_unit_123",
				"refresh":    30,
			},
			"video": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "video_ad_unit_456",
				"reward":     50,
			},
			"interstitial": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "interstitial_ad_unit_789",
				"frequency":  3,
			},
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: adsData,
	})
}
