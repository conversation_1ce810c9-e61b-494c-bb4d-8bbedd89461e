package handlers

import (
	"net/http"

	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// GetAdvInfo GETADVINFO - 获取广告信息
func GetAdvInfo(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")
	platform := c.Query("platform")

	if appName == "" {
		c.<PERSON>(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟广告信息
	advInfo := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"platform": platform,
		"ads": map[string]interface{}{
			"banner": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "banner_unit_123",
				"refresh":    30,
				"position":   "bottom",
				"size":       "320x50",
			},
			"video": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "video_unit_456",
				"reward":     100,
				"max_daily":  10,
			},
			"interstitial": map[string]interface{}{
				"enabled":      true,
				"ad_unit_id":   "interstitial_unit_789",
				"frequency":    5,
				"min_interval": 60,
			},
		},
		"config": map[string]interface{}{
			"timeout":     5000,
			"retry_count": 3,
			"test_mode":   false,
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: advInfo,
	})
}

// MaterialSS MATERIALSS - 素材接口
func MaterialSS(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")
	materialType := c.Query("type") // image, video, audio

	if appName == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟素材数据
	materials := []map[string]interface{}{
		{
			"id":         "material_001",
			"type":       "image",
			"name":       "游戏背景图",
			"url":        "https://example.com/materials/bg001.jpg",
			"size":       "1920x1080",
			"file_size":  1024000,
			"created_at": 1234567890,
		},
		{
			"id":         "material_002",
			"type":       "video",
			"name":       "游戏宣传视频",
			"url":        "https://example.com/materials/promo001.mp4",
			"duration":   30,
			"file_size":  5120000,
			"created_at": 1234567890,
		},
		{
			"id":         "material_003",
			"type":       "audio",
			"name":       "背景音乐",
			"url":        "https://example.com/materials/bgm001.mp3",
			"duration":   120,
			"file_size":  2048000,
			"created_at": 1234567890,
		},
	}

	// 根据类型过滤
	if materialType != "" {
		filteredMaterials := []map[string]interface{}{}
		for _, material := range materials {
			if material["type"] == materialType {
				filteredMaterials = append(filteredMaterials, material)
			}
		}
		materials = filteredMaterials
	}

	materialData := map[string]interface{}{
		"app_name":  appName,
		"version":   version,
		"type":      materialType,
		"materials": materials,
		"total":     len(materials),
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: materialData,
	})
}

// UuidInWhitelist UUID_IN_WHITE_LIST - UUID白名单检查
func UuidInWhitelist(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		uuid := c.Query("uuid")
		appName := c.Query("app_id")

		if uuid == "" || appName == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 查询数据库检查UUID是否在白名单中
		isInWhitelist, err := db.CheckWhitelist(appName, uuid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "数据库查询失败",
			})
			return
		}

		// 根据需求，返回简化的数据格式
		var data int
		if isInWhitelist {
			data = 1
		} else {
			data = 0
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: data,
		})
	}
}
