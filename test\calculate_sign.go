package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"sort"
	"strings"
)

func calculateSign(params map[string]string, secret string) string {
	// 参数按字母排序
	keys := make([]string, 0, len(params))
	for k := range params {
		if k != "sign" { // 排除sign参数本身
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 拼接参数
	var paramStr strings.Builder
	for i, key := range keys {
		if i > 0 {
			paramStr.WriteString("&")
		}
		paramStr.WriteString(key)
		paramStr.WriteString("=")
		paramStr.WriteString(params[key])
	}

	// 添加密钥
	paramStr.WriteString(secret)

	fmt.Printf("参数排序后: %v\n", keys)
	fmt.Printf("拼接字符串: %s\n", paramStr.String())

	// 计算MD5
	hash := md5.Sum([]byte(paramStr.String()))
	return hex.EncodeToString(hash[:])
}

func main() {
	// 你的请求参数
	params := map[string]string{
		"app_id":    "ywxg5ihgketop9gshnsh",
		"nonce":     "702ca5dca881910c50fb781ff0022297",
		"timestamp": "1753859722",
		"uuid":      "4b7ba46b",
	}

	secret := "H3qR7sL9xTmNb4cDpKjEwv8yZCiOa2fU"

	expectedSign := calculateSign(params, secret)
	yourSign := "d06062d206969ccb6a5f5ddfc0a1d846"

	fmt.Printf("期望的签名: %s\n", expectedSign)
	fmt.Printf("你的签名:   %s\n", yourSign)
	fmt.Printf("签名匹配:   %t\n", expectedSign == yourSign)
}
