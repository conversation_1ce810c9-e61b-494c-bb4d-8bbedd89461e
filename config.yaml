# 游戏日志服务配置文件

# 数据库配置
database:
  # 数据库类型: mongodb 或 sqlserver
  type: sqlserver

  # MongoDB配置
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records

  # SQL Server配置
  sqlserver:
    server: **********
    port: 14333
    database: IAA_MiniGames
    username: sa
    password: p@ssw0rd@456
    instance: "" # 实例名（可选）
    encrypt: false # 是否启用加密

# 服务器配置
server:
  port: 3001
  host: 0.0.0.0

  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - "*"

# 日志配置
logging:
  level: info # debug, info, warn, error
  format: text # text, json
  output: logs/app.log # stdout, stderr, 或文件路径如 logs/app.log

  # Gin HTTP访问日志格式配置
  gin_format:
    # 日志模板，支持的变量：
    # {time} - 时间, {method} - HTTP方法, {path} - 请求路径
    # {status} - 状态码, {latency} - 响应时间, {ip} - 客户端IP
    # {user_agent} - 用户代理, {error} - 错误信息

    # 预设模板（可选择使用）：
    # 简洁格式: "{method} {path} - {status} ({latency})"
    # 详细格式: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
    # 完整格式: "[{time}] {ip} \"{method} {path}\" {status} {latency} \"{user_agent}\""
    # 开发格式: "{method} {path} -> {status} in {latency}"

    template: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
    time_format: "2006-01-02 15:04:05"  # Go时间格式
    enable_color: false  # 是否启用颜色（文件输出建议false，控制台输出可以true）

# 安全配置
security:
  app_secret: "H3qR7sL9xTmNb4cDpKjEwv8yZCiOa2fU"

# BaseNet API配置
basenet:
  # 平台配置
  platforms:
    wechat:
      app_id: "your_wechat_app_id"
      app_secret: "your_wechat_app_secret"
    baidu:
      app_id: "your_baidu_app_id"
      app_secret: "your_baidu_app_secret"
    qq:
      app_id: "your_qq_app_id"
      app_secret: "your_qq_app_secret"

  # 游戏配置
  game_config:
    max_level: 100
    daily_reward: true
    maintenance: false
    force_update: false

  # 广告配置
  ads_config:
    banner:
      enabled: true
      refresh_interval: 30
    video:
      enabled: true
      reward_amount: 50
    interstitial:
      enabled: true
      frequency: 3
