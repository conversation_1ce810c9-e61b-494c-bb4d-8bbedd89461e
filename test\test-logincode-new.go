package main

import (
	"crypto/md5"
	"fmt"
	"os"
	"strings"
	"time"

	"iaa-gamelog/test/common"
)

// testLoginCodeValidation 测试新的登录码验证逻辑
func testLoginCodeValidation(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试新的登录码验证逻辑\n")

	// 测试用例1: 首次验证有效的验证码（无ca_code）
	fmt.Printf("  📝 测试场景1: 首次验证有效验证码\n")
	testCode := fmt.Sprintf("test_code_%d", time.Now().Unix())
	
	// 首先需要在数据库中插入一个有效的验证码记录（没有ca_code）
	// 这里我们假设数据库中已经有这样的记录，或者通过其他方式插入
	
	url := fmt.Sprintf("%s/check/login-code?app_name=test_app&version=1.0.0&code=%s", 
		config.BaseURL, testCode)
	
	resp, err := common.HTTPGet(config, url)
	if err != nil {
		common.PrintTestResult("首次验证有效验证码", false, err)
		return err
	}

	// 由于数据库中可能没有这个验证码，我们期望返回"验证码无效"
	if resp.Code == -1 && resp.Msg == "验证码无效" {
		common.PrintTestResult("首次验证不存在的验证码", true, nil)
		fmt.Printf("     ✓ 正确返回验证码无效\n")
	} else {
		common.PrintTestResult("首次验证不存在的验证码", false, 
			fmt.Errorf("期望返回验证码无效，实际: code=%d, msg=%s", resp.Code, resp.Msg))
	}

	return nil
}

// testLoginCodeWithCACode 测试带ca_code的验证码验证
func testLoginCodeWithCACode(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试带ca_code的验证码验证\n")

	// 测试用例: 验证不存在的code和ca_code组合
	testCode := "nonexistent_code"
	testCACode := "invalid_ca_code"
	
	url := fmt.Sprintf("%s/check/login-code?app_name=test_app&version=1.0.0&code=%s&ca_code=%s", 
		config.BaseURL, testCode, testCACode)
	
	resp, err := common.HTTPGet(config, url)
	if err != nil {
		common.PrintTestResult("验证不存在的code和ca_code", false, err)
		return err
	}

	// 期望返回"验证码过期"
	if resp.Code == -1 && resp.Msg == "验证码过期" {
		common.PrintTestResult("验证不存在的code和ca_code", true, nil)
		fmt.Printf("     ✓ 正确返回验证码过期\n")
	} else {
		common.PrintTestResult("验证不存在的code和ca_code", false, 
			fmt.Errorf("期望返回验证码过期，实际: code=%d, msg=%s", resp.Code, resp.Msg))
	}

	return nil
}

// testLoginCodeParameterValidation 测试参数验证
func testLoginCodeParameterValidation(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试参数验证\n")

	testCases := []struct {
		name        string
		appName     string
		code        string
		expectError bool
		description string
	}{
		{"缺少app_name", "", "test123", true, "应该返回缺少必要参数"},
		{"缺少code", "test_app", "", true, "应该返回缺少必要参数"},
		{"参数完整", "test_app", "test123", false, "参数完整，应该进行验证码检查"},
	}

	for _, tc := range testCases {
		fmt.Printf("  🔍 %s\n", tc.name)
		
		url := fmt.Sprintf("%s/check/login-code?app_name=%s&code=%s", 
			config.BaseURL, tc.appName, tc.code)
		
		resp, err := common.HTTPGet(config, url)
		if err != nil {
			if tc.expectError {
				common.PrintTestResult(tc.name, true, nil)
				fmt.Printf("     ✓ %s\n", tc.description)
			} else {
				common.PrintTestResult(tc.name, false, err)
			}
			continue
		}

		if tc.expectError {
			if resp.Code == -1 && resp.Msg == "缺少必要参数" {
				common.PrintTestResult(tc.name, true, nil)
				fmt.Printf("     ✓ %s\n", tc.description)
			} else {
				common.PrintTestResult(tc.name, false, 
					fmt.Errorf("期望返回参数错误，实际: code=%d, msg=%s", resp.Code, resp.Msg))
			}
		} else {
			// 对于参数完整的情况，我们期望进入验证码检查逻辑
			// 由于数据库中可能没有对应记录，期望返回"验证码无效"
			if resp.Code == -1 && resp.Msg == "验证码无效" {
				common.PrintTestResult(tc.name, true, nil)
				fmt.Printf("     ✓ %s\n", tc.description)
			} else {
				common.PrintTestResult(tc.name, false, 
					fmt.Errorf("期望进入验证码检查，实际: code=%d, msg=%s", resp.Code, resp.Msg))
			}
		}
	}

	return nil
}

// testMD5Generation 测试MD5生成逻辑
func testMD5Generation(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试MD5生成逻辑\n")

	// 测试已知的MD5值
	testCode := "test123"
	expectedMD5 := fmt.Sprintf("%x", md5.Sum([]byte(testCode)))
	
	fmt.Printf("  🔑 测试代码: %s\n", testCode)
	fmt.Printf("  🔑 期望MD5: %s\n", expectedMD5)
	
	// 验证MD5长度
	if len(expectedMD5) == 32 {
		common.PrintTestResult("MD5长度验证", true, nil)
		fmt.Printf("     ✓ MD5长度正确: 32位\n")
	} else {
		common.PrintTestResult("MD5长度验证", false, 
			fmt.Errorf("MD5长度错误，期望32位，实际: %d位", len(expectedMD5)))
	}

	return nil
}

// testCompleteWorkflow 测试完整的工作流程（需要手动设置数据库）
func testCompleteWorkflow(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试完整工作流程（需要手动数据库设置）\n")
	
	fmt.Printf("  ℹ️  注意: 此测试需要手动在数据库中设置测试数据\n")
	fmt.Printf("  ℹ️  请在ca_code_list表中插入以下记录:\n")
	fmt.Printf("     - app_name: 'test_workflow'\n")
	fmt.Printf("     - code: 'workflow_test_123'\n")
	fmt.Printf("     - ca_code: '' (空值)\n")
	fmt.Printf("  ℹ️  然后运行此测试以验证完整流程\n")
	
	// 这里可以添加实际的测试逻辑，但需要确保数据库中有对应的测试数据
	
	return nil
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	common.PrintTestHeader("BaseNet API - 新登录码验证功能测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./iaa-gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testLoginCodeParameterValidation(config); err != nil {
		hasError = true
	}

	if err := testLoginCodeValidation(config); err != nil {
		hasError = true
	}

	if err := testLoginCodeWithCACode(config); err != nil {
		hasError = true
	}

	if err := testMD5Generation(config); err != nil {
		hasError = true
	}

	if err := testCompleteWorkflow(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 新登录码验证功能测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 新登录码验证功能测试全部通过")
		fmt.Println("\n📋 测试总结:")
		fmt.Println("  ✓ 参数验证功能正常")
		fmt.Println("  ✓ 验证码不存在时正确返回错误")
		fmt.Println("  ✓ ca_code验证逻辑正常")
		fmt.Println("  ✓ MD5生成功能正常")
		fmt.Println("\n💡 提示:")
		fmt.Println("  - 要测试完整的验证码流程，需要在数据库中手动添加测试数据")
		fmt.Println("  - 建议使用数据库管理工具添加测试记录后再次运行测试")
	}
}
