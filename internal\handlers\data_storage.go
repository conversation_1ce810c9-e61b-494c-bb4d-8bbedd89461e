package handlers

import (
	"log"
	"net/http"
	"strings"

	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// DataSave DATA_SAVE - 保存游戏数据（需要签名验证）
func DataSave(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.DataSaveRequest

		// 支持多种数据格式：JSON 和表单数据
		contentType := c.GetHeader("Content-Type")
		if strings.Contains(contentType, "application/json") {
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, models.StandardResponse{
					Code: -1,
					Msg:  "请求参数错误: " + err.Error(),
				})
				return
			}
		} else {
			// 处理表单数据（application/x-www-form-urlencoded）
			if err := c.ShouldBind(&req); err != nil {
				c.JSO<PERSON>(http.StatusBadRequest, models.StandardResponse{
					Code: -1,
					Msg:  "请求参数错误: " + err.Error(),
				})
				return
			}
		}

		if req.AppName == "" || req.UUID == "" || req.DKey == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 使用现有的数据库接口保存数据
		// 将d_key作为gameName，uuid作为userID，d_data作为gameData
		recordID, action, err := db.SaveRecord(req.UUID, req.DKey, req.DData)
		if err != nil {
			// 输出详细错误信息到日志
			log.Printf("数据保存失败: userID=%s, gameName=%s, error=%v", req.UUID, req.DKey, err)
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "保存数据失败: " + err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: map[string]interface{}{
				"id":     recordID,
				"action": action,
			},
		})
	}
}

// DataGet DATA_GET - 获取游戏数据
func DataGet(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.DataGetRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" || req.DKey == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 使用现有的数据库接口获取数据
		records, total, err := db.GetRecords(req.UUID, req.DKey, 1, 0)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "获取数据失败",
			})
			return
		}

		if total == 0 {
			c.JSON(http.StatusOK, models.StandardResponse{
				Code: 0,
				Msg:  "success",
				Data: nil,
			})
			return
		}

		// 转换为统一格式
		unifiedRecords := database.ConvertToUnified(records)
		gameData := unifiedRecords[0].GameData

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: gameData,
		})
	}
}

// DataMultiGet DATA_MULTIGET - 批量获取游戏数据
func DataMultiGet(db database.DatabaseInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req models.DataMultiGetRequest
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求参数错误",
			})
			return
		}

		if req.AppName == "" || req.UUID == "" || req.DKeys == "" {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必要参数",
			})
			return
		}

		// 解析多个键名
		keys := strings.Split(req.DKeys, ",")
		result := make(map[string]interface{})

		for _, key := range keys {
			key = strings.TrimSpace(key)
			if key == "" {
				continue
			}

			// 获取每个键的数据
			records, total, err := db.GetRecords(req.UUID, key, 1, 0)
			if err != nil {
				result[key] = nil
				continue
			}

			if total == 0 {
				result[key] = nil
			} else {
				unifiedRecords := database.ConvertToUnified(records)
				result[key] = unifiedRecords[0].GameData
			}
		}

		c.JSON(http.StatusOK, models.StandardResponse{
			Code: 0,
			Msg:  "success",
			Data: result,
		})
	}
}
